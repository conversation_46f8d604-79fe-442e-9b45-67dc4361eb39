.filter-container {
  background-color: #efefef;
  font-family: MuseoSans-300;
  font-size: 10px;
}

h2 {
  color: #0071bc;
  font-family: MuseoSans-700;
  font-size: 1.2rem;
  margin-top: 0;
  margin-bottom: 15px;
  font-weight: 500;
  padding-top: 10px;
  text-align: left;
}

label {
  font-size: 0.8rem;
  color: #333;
  font-weight: 400;
  /* white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis; */
}

.form-select-sm {
  font-size: 0.9rem;
  color: #666;
  background-position: right 10px center;
  background-size: 10px;
  text-overflow: ellipsis;
  border-radius: 5px;
  height: 35px;
}

.form-select-sm:focus {
  border-color: #0071bc;
  box-shadow: 0 0 0 0.25rem rgba(0, 113, 188, 0.25);
}

.run-button {
  background-color: #0071bc;
  color: white;
  border: 2px solid #66a9d7;
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  width: 100%;
  transition: background-color 0.2s;
  height: 38px;
}

.run-button:hover {
  background-color: #005a95;
}

.run-button:active {
  background-color: #004b7c;
}

.filterBorder {
  border-radius: 8px;
  border: 1px solid #ccc;
  padding: 10px;
  display: grid;
  grid-template-columns: 1fr;
}

.separator {
  margin-left: 5px;
  margin-right: 5px;
}

select, option {
  font-family: MuseoSans-300, sans-serif;
}

/* ng-select custom styling */
.ng-select.custom {
  font-size: 0.9rem;
  font-family: MuseoSans-300, sans-serif;
  color: #666;
  background-position: right 10px center;
  background-size: 10px;
  text-overflow: ellipsis;
  border-radius: 5px;
  height: 35px;
}

.ng-select.custom .ng-select-container {
  min-height: 35px;
  border-radius: 5px;
  border: 1px solid #ced4da;
  background-color: #fff;
}

.ng-select.custom.ng-select-focused .ng-select-container {
  border-color: #0071bc;
  box-shadow: 0 0 0 0.25rem rgba(0, 113, 188, 0.25);
}

.ng-select.custom .ng-placeholder {
  color: #6c757d;
  font-size: 0.9rem;
}

.ng-select.custom .ng-value-container {
  padding-left: 12px;
  padding-right: 12px;
}

.ng-select.custom .ng-value {
  background-color: #0071bc;
  color: white;
  border-radius: 3px;
  padding: 2px 8px;
  margin: 2px;
  font-size: 0.8rem;
}

.ng-select.custom .ng-value .ng-value-label {
  color: white;
}

.ng-select.custom .ng-value .ng-value-icon {
  color: white;
}

.ng-select.custom .ng-value .ng-value-icon:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.ng-select.custom .ng-arrow-wrapper {
  width: 25px;
}

.ng-select.custom .ng-arrow-wrapper .ng-arrow {
  border-color: #999 transparent transparent;
  border-style: solid;
  border-width: 5px 5px 0;
}

.ng-select.custom.ng-select-opened .ng-arrow-wrapper .ng-arrow {
  border-color: transparent transparent #999;
  border-width: 0 5px 5px;
}

.ng-select.custom .ng-dropdown-panel {
  border: 1px solid #ced4da;
  border-radius: 5px;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.ng-select.custom .ng-dropdown-panel .ng-dropdown-panel-items .ng-optgroup {
  font-weight: 600;
  color: #0071bc;
  padding: 8px 12px 4px;
  font-size: 0.85rem;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.ng-select.custom .ng-dropdown-panel .ng-dropdown-panel-items .ng-option {
  padding: 8px 12px;
  font-size: 0.9rem;
  color: #333;
}

.ng-select.custom .ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-highlighted {
  background-color: #e3f2fd;
  color: #0071bc;
}

.ng-select.custom .ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-selected {
  background-color: #0071bc;
  color: white;
}

.ng-select.custom .ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-disabled {
  color: #6c757d;
}

/* Loading spinner styling */
.ng-select.custom .ng-spinner-loader {
  border-color: #0071bc transparent transparent;
}

/* Search input styling */
.ng-select.custom .ng-input > input {
  font-size: 0.9rem;
  color: #333;
}

/* Clear all button styling */
.ng-select.custom .ng-clear-wrapper {
  color: #6c757d;
}

.ng-select.custom .ng-clear-wrapper:hover {
  color: #0071bc;
}