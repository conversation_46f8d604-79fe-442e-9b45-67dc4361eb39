<div class="filter-container container">
  <h2>Quality Measure Filters</h2>

  <div class="filterBorder row-gap-2">

    <!-- Dynamic filters based on database configuration -->
    <div *ngFor="let config of getEnabledFilterConfigurations()" class="row">
      <div class="col-5 d-flex align-items-center">
        <label [for]="config.filterKey">
          {{ config.displayName }}
          <span *ngIf="config.isRequired" class="text-danger">*</span>
        </label>
        <!-- Help text tooltip -->
        <span *ngIf="config.helpText"
              class="ms-1 text-muted"
              [title]="config.helpText"
              style="cursor: help;">
          ℹ️
        </span>
      </div>
      <div class="col-7">

        <!-- Dropdown control -->
        <select *ngIf="config.controlType === 'dropdown'"
                class="form-select form-select-sm"
                [id]="config.filterKey"
                [ngModel]="getFilterValue(config.filterKey)"
                [name]="config.filterKey"
                [required]="config.isRequired"
                [disabled]="config.isReadOnly"
                (ngModelChange)="onFilterChange(config.filterKey, $event)">
          <option value="" disabled>{{ config.placeholderText || 'Select an option' }}</option>
          <option *ngFor="let option of getFilterOptions(config.filterKey)"
                  [value]="option.value">
            {{ option.label }}
          </option>
        </select>

        <!-- Multi-select control using ng-select -->
        <div *ngIf="config.controlType === 'multiselect'">
          <!-- Location multiselect with search and groups -->
          <ng-select *ngIf="config.filterKey === 'patientLocation'"
                     [items]="locationOptions$ | async"
                     [multiple]="true"
                     [closeOnSelect]="false"
                     [clearSearchOnAdd]="false"
                     [hideSelected]="false"
                     [loading]="locationLoading"
                     [typeahead]="locationSearchInput$"
                     groupBy="group"
                     bindLabel="label"
                     bindValue="value"
                     [placeholder]="config.placeholderText || 'Search and select locations'"
                     [clearable]="true"
                     [searchable]="true"
                     [disabled]="config.isReadOnly"
                     [ngModel]="selectedLocations"
                     (ngModelChange)="onLocationSelectionChange($event)"
                     class="custom">
          </ng-select>

          <!-- Standard multiselect for other filters -->
          <ng-select *ngIf="config.filterKey !== 'patientLocation'"
                     [items]="getFilterOptions(config.filterKey)"
                     [multiple]="true"
                     [closeOnSelect]="false"
                     bindLabel="label"
                     bindValue="value"
                     [placeholder]="config.placeholderText || 'Select options'"
                     [clearable]="true"
                     [searchable]="true"
                     [disabled]="config.isReadOnly"
                     [ngModel]="getFilterValue(config.filterKey)"
                     (ngModelChange)="onFilterChange(config.filterKey, $event)"
                     class="custom">
          </ng-select>
        </div>

        <!-- Text input control -->
        <input *ngIf="config.controlType === 'text'"
               type="text"
               class="form-control form-control-sm"
               [id]="config.filterKey"
               [ngModel]="getFilterValue(config.filterKey)"
               [name]="config.filterKey"
               [required]="config.isRequired"
               [readonly]="config.isReadOnly"
               [placeholder]="config.placeholderText || ''"
               [attr.minlength]="config.validationRules?.minLength"
               [attr.maxlength]="config.validationRules?.maxLength"
               [attr.pattern]="config.validationRules?.pattern"
               (ngModelChange)="onFilterChange(config.filterKey, $event)">

        <!-- Number input control -->
        <input *ngIf="config.controlType === 'number'"
               type="number"
               class="form-control form-control-sm"
               [id]="config.filterKey"
               [ngModel]="getFilterValue(config.filterKey)"
               [name]="config.filterKey"
               [required]="config.isRequired"
               [readonly]="config.isReadOnly"
               [placeholder]="config.placeholderText || ''"
               [attr.min]="config.validationRules?.min"
               [attr.max]="config.validationRules?.max"
               (ngModelChange)="onFilterChange(config.filterKey, $event)">

        <!-- Date input control -->
        <input *ngIf="config.controlType === 'date'"
               type="date"
               class="form-control form-control-sm"
               [id]="config.filterKey"
               [ngModel]="getFilterValue(config.filterKey)"
               [name]="config.filterKey"
               [required]="config.isRequired"
               [readonly]="config.isReadOnly"
               [attr.min]="config.validationRules?.min"
               [attr.max]="config.validationRules?.max"
               (ngModelChange)="onFilterChange(config.filterKey, $event)">

        <!-- Checkbox control -->
        <div *ngIf="config.controlType === 'checkbox'" class="form-check">
          <input type="checkbox"
                 class="form-check-input"
                 [id]="config.filterKey"
                 [ngModel]="getFilterValue(config.filterKey)"
                 [name]="config.filterKey"
                 [required]="config.isRequired"
                 [readonly]="config.isReadOnly"
                 [disabled]="config.isReadOnly"
                 (ngModelChange)="onFilterChange(config.filterKey, $event)">
          <label class="form-check-label" [for]="config.filterKey">
            {{ config.placeholderText || config.displayName }}
          </label>
        </div>

      </div>
    </div>

    <!-- Loading indicator while filter configurations are being loaded -->
    <div *ngIf="filterConfigurations.length === 0" class="row">
      <div class="col-12 text-center">
        <div class="spinner-border spinner-border-sm" role="status">
          <span class="visually-hidden">Loading filters...</span>
        </div>
        <span class="ms-2">Loading filter configurations...</span>
      </div>
    </div>

    <!-- No filters available message -->
    <div *ngIf="filterConfigurations.length > 0 && getEnabledFilterConfigurations().length === 0" class="row">
      <div class="col-12 text-center text-muted">
        No filters are currently enabled for this report.
      </div>
    </div>

    <div class="border border-top separator"></div>

    <!-- Action buttons -->
    <div class="d-flex gap-2">
      <button class="run-button"
              (click)="applyFilters()"
              [disabled]="filterConfigurations.length === 0">
        Run
      </button>
    </div>
    <div class="border border-top separator"></div>

    <div class="d-flex gap-2">

      <p>Debugging:</p>
      <!-- Reset button for testing -->
      <button type="button"
              class="btn btn-outline-secondary btn-sm"
              (click)="resetAllFiltersToDefaults()"
              [disabled]="filterConfigurations.length === 0"
              title="DEBUG: Reset to default values">
        Reset All
      </button>

      <button type="button"
              class="btn btn-outline-warning btn-sm"
              (click)="resetFiltersExceptMeasureGroup()"
              [disabled]="filterConfigurations.length === 0"
              title="DEBUG: Reset all filters except Measure Group">
        Reset Others
      </button>
    </div>

  </div>

  <!-- Debug information (remove in production) -->
  <div class="mt-3" *ngIf="filterConfigurations.length > 0">
    <details>
      <summary>Debug: Filter Configurations</summary>
      <pre>{{ filterConfigurations | json }}</pre>
    </details>
    <details>
      <summary>Debug: Current Filter Values</summary>
      <pre>{{ filters | json }}</pre>
    </details>
  </div>

</div>
