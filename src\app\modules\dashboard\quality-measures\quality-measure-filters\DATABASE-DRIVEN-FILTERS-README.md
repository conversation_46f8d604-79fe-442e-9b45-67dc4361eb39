# Database-Driven Filter Configuration

This document outlines the design for converting the quality-measure-filters component into a generic, database-driven report filter component.

## Overview

The current quality-measure-filters component has hardcoded filter options and configurations. The new approach will use a metadata-driven system where filter configurations are stored in a database and retrieved via API calls.

## Key Benefits

1. **Dynamic Configuration**: Filters can be configured per report without code changes
2. **Flexible Control Types**: Support for dropdown, multiselect, text, date, number, and checkbox controls
3. **Conditional Logic**: Filters can depend on other filters for dynamic option loading
4. **Validation Rules**: Built-in validation constraints defined in the database
5. **Easy Maintenance**: Add/remove/modify filters through database configuration

## Implementation Files

### Core Files Modified
- `quality-measure-filters.component.ts` - Added FilterConfiguration interface and mock API methods
- `mock-filter-api-examples.json` - Example API responses for different report types
- `quality-measure-filters-dynamic.component.html` - Example template using database-driven approach

### New Interfaces

```typescript
export interface FilterConfiguration {
  filterKey: string;                    // Must match QualityMeasureFilters property names
  displayName: string;                  // Label shown to user
  controlType: 'dropdown' | 'multiselect' | 'text' | 'date' | 'number' | 'checkbox';
  isEnabled: boolean;                   // Whether to show this filter
  isReadOnly: boolean;                  // Whether filter is editable
  isRequired: boolean;                  // Whether filter is required
  defaultValue: string | null;          // Default value to set
  optionsSource: string | null;         // API endpoint for options (null = use embedded)
  embeddedOptions?: FilterOption[];     // Static options when optionsSource is null
  sortOrder: number;                    // Display order
  placeholderText?: string;             // Placeholder text
  validationRules?: ValidationRules;    // Client-side validation
  dependsOn?: string[];                 // Filters this depends on
  cssClass?: string;                    // Custom CSS classes
  helpText?: string;                    // Help text for users
}
```

## API Endpoints

### Get Report Filters
```
GET /api/reports/{reportId}/filters
```
Returns an array of FilterConfiguration objects for the specified report.

### Get Filter Options
```
GET /api/lookups/{optionsSource}
```
Returns FilterOption arrays for filters that use API-sourced options.

## Example API Responses

### Quality Measures Report
```json
[
  {
    "filterKey": "measureGroup",
    "displayName": "Measure Group",
    "controlType": "dropdown",
    "isEnabled": true,
    "isReadOnly": false,
    "isRequired": true,
    "defaultValue": "targeted",
    "optionsSource": null,
    "embeddedOptions": [
      {"value": "targeted", "label": "Targeted Quality Measures"},
      {"value": "HIV", "label": "HHS HIV Guidelines of Care"}
    ],
    "sortOrder": 1,
    "placeholderText": "Select a measure group"
  }
]
```

### HIV-Specific Report (with readonly filter)
```json
[
  {
    "filterKey": "hivStatus",
    "displayName": "HIV Status",
    "controlType": "dropdown",
    "isEnabled": true,
    "isReadOnly": true,
    "isRequired": true,
    "defaultValue": "1",
    "optionsSource": null,
    "embeddedOptions": [
      {"value": "1", "label": "Positive"}
    ],
    "sortOrder": 1,
    "helpText": "This report only shows HIV positive patients"
  }
]
```

## Migration Strategy

### Phase 1: Backward Compatibility
- Keep existing filterOptions structure
- Add FilterConfiguration interface and mock methods
- Test with mock data

### Phase 2: API Integration
- Implement actual API calls in QualityMeasuresService
- Replace mock methods with real service calls
- Add error handling and loading states

### Phase 3: Template Migration
- Update HTML template to use dynamic approach
- Add support for all control types
- Implement validation and help text display

### Phase 4: Database Schema
- Create filter configuration tables
- Migrate existing filter definitions to database
- Implement admin interface for filter management

## Database Schema (Proposed)

### report_filters table
```sql
CREATE TABLE report_filters (
  id INT PRIMARY KEY AUTO_INCREMENT,
  report_id INT NOT NULL,
  filter_key VARCHAR(50) NOT NULL,
  display_name VARCHAR(100) NOT NULL,
  control_type ENUM('dropdown', 'multiselect', 'text', 'date', 'number', 'checkbox'),
  is_enabled BOOLEAN DEFAULT TRUE,
  is_readonly BOOLEAN DEFAULT FALSE,
  is_required BOOLEAN DEFAULT FALSE,
  default_value VARCHAR(255),
  options_source VARCHAR(255),
  sort_order INT DEFAULT 0,
  placeholder_text VARCHAR(255),
  help_text TEXT,
  css_class VARCHAR(100),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### filter_options table (for embedded options)
```sql
CREATE TABLE filter_options (
  id INT PRIMARY KEY AUTO_INCREMENT,
  filter_id INT NOT NULL,
  option_value VARCHAR(255) NOT NULL,
  option_label VARCHAR(255) NOT NULL,
  sort_order INT DEFAULT 0,
  FOREIGN KEY (filter_id) REFERENCES report_filters(id)
);
```

## Usage Examples

### Component Usage
```typescript
// Load filters for a specific report
this.qualityMeasuresService.getReportFilters(reportId)
  .subscribe(configs => {
    this.filterConfigurations = configs;
  });

// Get options for a specific filter
const options = this.getFilterOptions('patientLocation');

// Check if filter is required
const isRequired = this.isFilterRequired('year');
```

### Template Usage
```html
<!-- Dynamic filter rendering -->
<div *ngFor="let config of getEnabledFilterConfigurations()">
  <label>{{ config.displayName }}</label>
  <select *ngIf="config.controlType === 'dropdown'"
          [(ngModel)]="filters[config.filterKey]">
    <option *ngFor="let option of getFilterOptions(config.filterKey)"
            [value]="option.value">
      {{ option.label }}
    </option>
  </select>
</div>
```

## Testing

### Mock Data
The component includes comprehensive mock data that simulates various scenarios:
- Standard quality measures report
- HIV-specific report with readonly filters
- Custom KPI report with different control types
- Simple report with minimal filters

### Test Cases
1. Filter loading and display
2. Dynamic option loading based on dependencies
3. Validation rule enforcement
4. Required field handling
5. Readonly filter behavior

## Future Enhancements

1. **Filter Dependencies**: Implement cascading filter logic
2. **Custom Validation**: Add server-side validation rules
3. **Filter Groups**: Group related filters together
4. **Conditional Display**: Show/hide filters based on other filter values
5. **User Preferences**: Save user's preferred filter settings
6. **Filter Presets**: Allow saving and loading filter combinations

## Compatibility Notes

- Maintains backward compatibility with existing QualityMeasureFilters interface
- Existing filterOptions structure is preserved as fallback
- Current template continues to work during migration
- No breaking changes to parent components
