import { Component, ElementRef, Injectable, OnInit, QueryList, ViewChildren } from '@angular/core';
import { UserContext } from 'src/app/shared-services/user-context/user-context.service';
import {
  ApiRoutes,
  ApiTypes,
} from 'src/app/shared-services/ep-api-handler/api-option-enums';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { ChangePasswordDialogComponent } from '../dialogs/change-password-dialog/change-password-dialog.component';
import { LayoutService } from '../services/layout/layout.service';
import { Router } from '@angular/router';
import { Subject, Subscription } from 'rxjs';
import { ISite } from '../models/site-model';
import { IChorusAccessViewModel, RoleTypes } from 'src/app/shared-services/user-context/models/user-security-model';
import { MobileDeviceManagementComponent } from '../dialogs/MobileDeviceManagement/MobileDeviceManagement.component';
import { TimeoutSessionService } from 'src/app/shared-services/timeout-session-service';
import { EpividianCommon } from 'src/app/modules/utility/EpividianCommon';
import { SupportDetailsDialogComponent } from '../dialogs/support-details-dialog/support-details-dialog.component';
import { IChorusResponseToken } from 'src/app/shared-services/ep-api-handler/models/login-response.model';
import { EnvironmentService } from '../dialogs/support-details-dialog/supportService.service';
import { SwUpdate } from '@angular/service-worker';
import { DatePipe } from '@angular/common';
import { MatExpansionPanel } from '@angular/material/expansion';
import { IMenuSections } from '../models/menu-item.model';
import { SiteNameService } from "src/app/shared-services/site-name.service";

@Injectable({
  providedIn: 'root'
})
@Component({
  selector: 'epividian-sidenav',
  templateUrl:'./nav.component.html',
  styleUrls: ['./nav.component.scss']
})

export class NavComponent implements OnInit  {
  @ViewChildren('expansionPanel') expansionPanels!: QueryList<MatExpansionPanel>;

  public menuSections$: IMenuSections[] = [];
  panelOpenState: boolean = false;
  allExpandState = false;
  hasOutreach: boolean = false;
  hasHuddleAccess: boolean = false;
  hasAdminAccess: boolean = false;
  private reportPath: string = "";
  public rootSharePath = `../Documents/${btoa('root/')}`;
  public defaultReportPath: string = "";
  userName: string = '';
  firstName: string = '';
  lastName: string = '';
  email: string = '';
  currentSiteName: string = '';
  siteList$: Subject<ISite[]> = new Subject<ISite[]>();
  siteList: ISite[] = [];
  currentSite$: Subject<ISite> = new Subject<ISite>();
  openDialog: MatDialogRef<ChangePasswordDialogComponent, any> = {} as MatDialogRef<ChangePasswordDialogComponent, any>;
  openMdmDialog: MatDialogRef<MobileDeviceManagementComponent, any> = {} as MatDialogRef<MobileDeviceManagementComponent, any>;
  openSupportDialog: MatDialogRef<SupportDetailsDialogComponent, any> = {} as MatDialogRef<SupportDetailsDialogComponent, any>;
  accessVM: IChorusAccessViewModel[] = [];
  lblDataLoadWarning = 'Database is updating. Records may be incomplete. Please try again in an hour.';
  siteId: number = 0;
  showSupportLink: boolean = false;
  private sessionObj: IChorusResponseToken = {} as IChorusResponseToken;
  private lastJwtRefreshCheck: Date = new Date();
  isLoadingChorusAccess: boolean = false;
  collapse: boolean = false;

  private pageSubscriptions: Subscription = new Subscription;
  tokenExpirationSubscription: Subscription = new Subscription;
  public supportMenu: Subject<boolean>  = new Subject<boolean>();
  public autoRefreshOffset: number = 0;
  datepipe: DatePipe = new DatePipe('en-US');
  public lblDateLastUpdate: string = "";

  constructor(
    public layoutService: LayoutService,
    private router: Router,
    public elem: ElementRef,
    public userContext: UserContext,
    public dialog: MatDialog,
    private timeoutSessionService: TimeoutSessionService,
    private epividianCommon: EpividianCommon,
    private envService: EnvironmentService,
    private swUpdate: SwUpdate,
    private siteNameService: SiteNameService
    ) {}

  ngOnInit() {
    this.layoutService.showSpinner();
    //will Remove this later after support role is added
    //toggles support menu link
    this.pageSubscriptions.add(
      this.envService.ShowSupportMenuItem().subscribe((show) => {
        this.showSupportLink = show;
    }));

    this.sessionObj = this.epividianCommon.GetSessionObjFromLocalStorage() || {} as IChorusResponseToken;
    let sessionTimeout = this.epividianCommon.LoadSessionTimeout(this.sessionObj);
    this.autoRefreshOffset = this.epividianCommon.percentToNearestWholeNumber(sessionTimeout, 15)

    if (this.userContext.GetCurrentSiteValue() != 0) {
      this.siteId = this.userContext.GetCurrentSiteValue();
    }

    this.userContext.getHasOutReach().subscribe( outReach => {
      this.hasOutreach = outReach;
    });

    // Check all feature and role-based access
    this.checkAllAccess();

    // We don't need this direct check anymore since we're using the site-specific check in checkAdminAccess
    // The checkAdminAccess method will be called and will set hasAdminAccess correctly

    this.pageSubscriptions.add(
      this.userContext.getCurrentSite().subscribe((siteId: number) => {
        // Start monitoring user inactivity
        this.timeoutSessionService.createInactivityExpireTime(new Date());

        this.siteId = siteId;
        if (this.swUpdate.isEnabled) {
          this.swUpdate.checkForUpdate();
        }

        if (this.siteId.toString()!="0")
        {
          this.pageSubscriptions.add(
            this.layoutService.GetDateLastUpdated(this.siteId.toString()).subscribe(lastUpdateDate => {
              this.lblDateLastUpdate = lastUpdateDate;
            })
          );
        }
      })
    );

    this.tokenRefreshChecker(sessionTimeout);

    this.pageSubscriptions.add(
      this.tokenExpirationSubscription = this.timeoutSessionService.tokenExpired.subscribe(expired => {
        if (expired) {
          this.timeoutSessionService.showSessionTimeoutMessage();
        }
      })
    );

    if (!this.isLoadingChorusAccess) {
      this.isLoadingChorusAccess = true;

      this.pageSubscriptions.add(
        this.userContext.LoadUsersChorusAccess().subscribe((s: IChorusAccessViewModel[]) => {

          try {

            if (this.siteId==0)
              {
                let defaultSite = s.find(x => x.isDefault == true);
                this.siteId = Number(defaultSite?.siteId);
              }
              else
              {
                let currentSite = s.find(x => x.siteId == this.siteId);
                this.siteId = Number(currentSite?.siteId);
              }
              this.userContext.SetCurrentSite(this.siteId);
              this.userContext.SetUserSecurity(s);
              this.layoutService.setNavandReload(this.siteId);
              this.accessVM = s;

              this.pageSubscriptions.add(this.userContext.GetUserInfoSub().subscribe (s => {
                this.userName = this.userContext.DisplayName();
                this.email = this.userContext.GetUserName();
                this.firstName = s.firstNm;
                this.lastName = s.lastNm;
              }));

              this.pageSubscriptions.add(
                this.userContext.getSites().subscribe((sites) => {
                  if (this.userContext.isSiteListEmpty()==true) {
                    this.userContext.SetSiteList(sites);
                  }
                  let site = sites.find((f) => f.site == this.siteId) as ISite;
                  this.siteList$.next(sites);
                  this.currentSite$.next(site);
                  this.siteList = sites;
                  this.siteId = site.site;
                  this.defaultReportPath = "/Dashboard/Report/" + this.siteId + "/CustomQuery/Criteria";
                  //Loads Users Security Site List selecting default site

                  if (this.siteId==0)
                  {
                    this.siteId =parseInt(
                      s.find((f) => f.isDefault == true)?.siteId.toString() + ''
                    );
                  }
                  this.userContext.SetOutReach(this.accessVM.filter((f) => f.siteId == this.siteId)[0].enableOutreachRetention);
                })
              );

          } catch (error) {
            console.error('Error occurred loading user data:', error);
          }

          this.isLoadingChorusAccess = false;

        })
      );
    };

    this.currentSite$.subscribe(s => {
      this.currentSiteName = s.name;
      this.siteNameService.updateCurrentSiteName(this.currentSiteName);
    });

  }

  tokenRefreshChecker(sessionTimeout: number) {
    this.pageSubscriptions.add(
      this.timeoutSessionService.getWindowLastActiveTime().subscribe((lastActiveTime) => {
        this.lastJwtRefreshCheck = this.epividianCommon.readFromStorage("lastRefreshCheck") as Date;
        if (!this.lastJwtRefreshCheck)
        {
          this.lastJwtRefreshCheck = new Date();
          this.epividianCommon.upsertToStorage("lastRefreshCheck", this.lastJwtRefreshCheck, true, true);
        }
        else
        {
          this.lastJwtRefreshCheck = new Date(this.lastJwtRefreshCheck);
        }

        this.timeoutSessionService.createInactivityExpireTime(lastActiveTime);

        // If it has been less than 60 seconds between the lastActivity time and jwt refresh check then skip.
        if (lastActiveTime.getTime() - this.lastJwtRefreshCheck.getTime()  > 60000)
        {
          this.lastJwtRefreshCheck = new Date();

          this.sessionObj = this.epividianCommon.GetSessionObjFromLocalStorage() || {} as IChorusResponseToken;
          const sessionExpiresTime = this.epividianCommon.convertUtcStringToDatePlusMin(
                  this.sessionObj.expires,
                  sessionTimeout,
                  this.autoRefreshOffset
                ).getTime();

          this.timeoutSessionService.checkIfSessionExpiredOrAutoRefreshNeeded(lastActiveTime, new Date(sessionExpiresTime))
          .then(sessionRefreshed => {
            if (sessionRefreshed!=null) {
              if (sessionRefreshed.action == true) {
                this.userContext.apihandler.setbearToken(sessionRefreshed.jwtToken.access_token);
                this.sessionObj = sessionRefreshed.jwtToken;
                //console.log('Session was Auto Refreshed');
              }
              else {
                //console.log('Session was Still Valid');
              }
            }
          })
          .catch(error => {
            console.error('Error occurred while checking session or refreshing token:', error);
          });
        }
      })
    );

  }

  //Used by Site Selection to Set and load current sites menu
  /**
   * Central method to check all feature and role-based access
   * This method should be called whenever the site changes or when the app initializes
   * Add all new access checks to this method
   */
  private checkAllAccess(): void {
    // Get the current site ID
    //const currentSiteId = this.userContext.GetCurrentSiteValue();

    // Check Huddle feature access
    this.checkHuddleAccess();

    // Check Admin role access
    this.checkAdminAccess();

    // Add any additional access checks here in the future
    // Example: this.checkReportsAccess();
  }

  /**
   * Checks if the user has access to the Huddle feature
   */
  private checkHuddleAccess(): void {
    this.userContext.checkFeatureAccess('Huddle', null).subscribe(hasAccess => {
      this.hasHuddleAccess = hasAccess;
    });
  }

  /**
   * Checks if the user has access to the Admin section
   * Uses the Epividian_Admin role to determine access
   * Checks specifically on the current site
   */
  private checkAdminAccess(): void {
    // Get the current site ID
    const currentSiteId = this.userContext.GetCurrentSiteValue();

    // Check if the user has the Epividian_Admin role on the current site
    this.userContext.hasRole(RoleTypes.Epividian_Admin, currentSiteId, false).subscribe(hasRoleOnCurrentSite => {
    //console.log('Admin access on current site:', hasRoleOnCurrentSite);
    this.hasAdminAccess = hasRoleOnCurrentSite;
    });
  }

  public SetCurrentSite(item: ISite) {
    this.layoutService.showSpinner();

    this.currentSite$.next(item);
    this.layoutService.setNavandReload(item.site);
    this.userContext.SetCurrentSite(item.site);

    // Check all access permissions for the new site
    // This is important to update the menu items based on the new site's permissions
    this.checkAllAccess();

    this.setDataLoadStatusMsg(item.site);
    this.userContext.SetOutReach(this.accessVM.filter((f) => f.siteId == item.site)[0].enableOutreachRetention);
    let adjustedSiteUrl = "/Dashboard"; ;
    if (this.router.url.includes('/Dashboard'))
    {
      this.router.navigate([adjustedSiteUrl], { replaceUrl: false }).then(() => {
        window.location.reload();
      });
    }
    else
    {
      this.router.navigate([adjustedSiteUrl], { replaceUrl: false });
    }
  }

  public openChangePasswordDialog() {
    this.openDialog = this.dialog.open(ChangePasswordDialogComponent, {
      height: 'auto',
      width: 'auto',
      panelClass: 'popup-modalbox',
      disableClose: true
    });
  }

  public openMobileDeviceDialog() {
    this.openMdmDialog = this.dialog.open(MobileDeviceManagementComponent, {
      panelClass: 'DeviceManagementDialog'
    });
  }

  public openSupportDetails() {
    this.dialog.open(SupportDetailsDialogComponent, {
      width: 'auto',
      height: 'auto',
      panelClass: 'support-details-dialog',
      data: this.timeoutSessionService.getSessionState()
    });

  }



  public openHelpWindow() {
    this.openCenteredWindow("HelpScreen/"+ this.siteId.toString(), 1280, 720);
  }

  openCenteredWindow(url, w, h) {
    // Calculate the position for the new window to be centered
    const dualScreenLeft = window.screenLeft !== undefined ? window.screenLeft : window.screenX;
    const dualScreenTop = window.screenTop !== undefined ? window.screenTop : window.screenY;

    const width = window.innerWidth ? window.innerWidth : document.documentElement.clientWidth ? document.documentElement.clientWidth : screen.width;
    const height = window.innerHeight ? window.innerHeight : document.documentElement.clientHeight ? document.documentElement.clientHeight : screen.height;

    const left = ((width - w) / 2) + dualScreenLeft;
    const top = ((height - h) / 2) + dualScreenTop;

    // Open the window
    const newWindow = window.open(url, '_blank', `resizable=yes, width=${w}, height=${h}, top=${top}, left=${left}`);

    // Focus on the new window if it opened successfully
    if (newWindow && newWindow.focus) {
        newWindow.focus();
    }
}

  //clears local storage values to kill session.
  public signOut() {
    let logoutRoute = ApiRoutes.Logout.replace('{{fromInactive}}', false.toString());
    this.userContext.apihandler.Post(ApiTypes.AuthProvider, logoutRoute, '', true, true).subscribe((s) => {
      this.userContext.ClearSession();
      this.timeoutSessionService.clearSessionTracker();
      this.router.navigate(['/']);
    });
  }

  public hasError = (controlName: string, errorName: string) => {
    //return this.ownerForm.controls[controlName].hasError(errorName);
  };

  public setDataLoadStatusMsg(siteId: number) {
    let dataLoadStatus = this.accessVM.find(
      (f) => f.siteId == siteId
    )?.isDataLoading;
    if (dataLoadStatus) {

      // Emit the data load status to the layout service
      this.layoutService.setDataLoadStatus(this.lblDataLoadWarning, 'alertRed.svg');
    }
    else
    {
      // Clear the data load status
      this.layoutService.setDataLoadStatus("", "");
    }
  }

  /*
  public toggleSupportMenuItem(): void {
    this.showSupportLink = !this.showSupportLink;
  }

  public toggleSideNav(): void {
    this.collapse = !this.collapse;
  }
*/

  ngOnDestroy(): void {
    // Complete the destroyed$ subject to clean up subscriptions
    this.pageSubscriptions.unsubscribe();
    this.siteList$.unsubscribe();
    this.currentSite$.unsubscribe();

  }
}
