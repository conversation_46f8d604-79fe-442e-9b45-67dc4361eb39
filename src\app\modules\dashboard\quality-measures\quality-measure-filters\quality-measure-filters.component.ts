import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { QualityMeasuresService } from '../services/quality-measures.service';
import { HIVStatus, HCVStatus } from '../../panels/models/patient-criteria.model';

// Define interfaces for our filter options and values
export interface FilterOption {
  value: string;
  label: string;
}

export interface QualityMeasureFilters {
  measureGroup: string;
  patientLocation: string;
  provider: string;
  year: string;
  quarter: string;
  patientStatus: string;
  sex: string;
  hivStatus: string;
  hcvStatus: string;
  insuranceType: string;
  lastProviderVisit: string;
}

// New interfaces for database-driven filter configuration
export interface FilterConfiguration {
  filterKey: string;
  displayName: string;
  controlType: 'dropdown' | 'multiselect' | 'text' | 'date' | 'number' | 'checkbox';
  isEnabled: boolean;
  isReadOnly: boolean;
  isRequired: boolean;
  defaultValue: string | null;
  optionsSource: string | null; // API endpoint or null for embedded options
  embeddedOptions?: FilterOption[]; // Embedded options when optionsSource is null
  sortOrder: number;
  placeholderText?: string;
  validationRules?: {
    minLength?: number;
    maxLength?: number;
    pattern?: string;
    min?: number;
    max?: number;
  };
  dependsOn?: string[]; // Array of filterKeys this filter depends on
  cssClass?: string;
  helpText?: string;
}

@Component({
  selector: 'app-quality-measure-filters',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './quality-measure-filters.component.html',
  styleUrl: './quality-measure-filters.component.css'
})
export class QualityMeasureFiltersComponent implements OnInit {
  @Output() filtersChanged = new EventEmitter<QualityMeasureFilters>();

  // Initialize filters with default values
  filters: QualityMeasureFilters = {
    measureGroup: 'targeted',
    patientLocation: 'any',
    provider: 'any',
    year: '2024',
    quarter: 'any',
    patientStatus: 'active',
    sex: 'any',
    hivStatus: '-1', // Default value from HIVStatus (-1 = All)
    hcvStatus: '-1', // Default value from HCVStatus (-1 = All)
    insuranceType: 'any',
    lastProviderVisit: 'any'
  };

  // Database-driven filter configurations (mock API response)
  filterConfigurations: FilterConfiguration[] = [];

  // Filter options with default values
  filterOptions = {
    measureGroups: [
      { value: 'targeted', label: 'Targeted Quality Measures' },
      { value: 'HIV', label: 'HHS HIV Guidelines of Care' },
      { value: 'Other', label: 'Other Quality Measures' },
      { value: 'custom', label: 'Custom KPIs' },
      { value: 'PQRS', label: '2016 - PQRS Measures' },
      { value: 'MIPS', label: '2025 - MIPS Measures' },
      { value: 'VAX', label: 'Vaccines' },
      { value: 'Bonus', label: 'Bonus Measures' },
      { value: 'PCF', label: 'Primary Care First' }
    ],
    patientLocations: [
      { value: 'any', label: 'Any' }
      // Will be populated from API
    ],
    providers: [
      { value: 'any', label: 'Any' },
      { value: 'dr-smith', label: 'Dr. Smith' },
      { value: 'dr-johnson', label: 'Dr. Johnson' },
      { value: 'dr-williams', label: 'Dr. Williams' },
      { value: 'dr-brown', label: 'Dr. Brown' },
      { value: 'dr-jones', label: 'Dr. Jones' }
    ],
    years: [
      { value: 'any', label: 'Any' },
      { value: '2025', label: '2025' },
      { value: '2024', label: '2024' },
      { value: '2023', label: '2023' },
      { value: '2022', label: '2022' },
      { value: '2021', label: '2021' }
    ],
    quarters: [
      { value: 'any', label: 'Any' },
      { value: 'q1', label: 'Q1' },
      { value: 'q2', label: 'Q2' },
      { value: 'q3', label: 'Q3' },
      { value: 'q4', label: 'Q4' }
    ],
    patientStatuses: [
      { value: 'active', label: 'Active Patients' },
      { value: 'inactive', label: 'Inactive Patients' },
      { value: 'all', label: 'All Patients' }
    ],
    sexes: [
      { value: 'any', label: 'Any' },
      { value: 'male', label: 'Male' },
      { value: 'female', label: 'Female' },
      { value: 'other', label: 'Other' }
    ],
    // Using HIVStatus from patient-criteria.model.ts
    hivStatuses: HIVStatus.map(status => ({
      value: status.value,
      label: status.text
    })),
    // Using HCVStatus from patient-criteria.model.ts
    hcvStatuses: HCVStatus.map(status => ({
      value: status.value,
      label: status.text
    })),
    insuranceTypes: [
      { value: 'any', label: 'Any' },
      { value: 'commercial', label: 'Commercial Insurance' },
      { value: 'private', label: 'Private' },
      { value: 'medicaid', label: 'Medicaid' },
      { value: 'medicare', label: 'Medicare' },
      { value: 'medicare-advantage', label: 'Medicare Advantage' },
      { value: 'medicare-supplement', label: 'Medicare Supplement' },
      { value: 'not-classified', label: 'Not Classified' },
      { value: 'not-followed', label: 'Not Followed' },
      { value: 'other', label: 'Other' },
      { value: 'other-grant', label: 'Other Grant' },
      { value: 'pharma-study', label: 'Pharma Study' },
      { value: 'uninsured', label: 'Uninsured' },
      { value: 'va', label: 'Veterans Health Administration' }
    ],
    lastProviderVisits: [
      { value: 'any', label: 'Any' },
      { value: 'last-30-days', label: 'Last 30 Days' },
      { value: 'last-90-days', label: 'Last 90 Days' },
      { value: 'last-6-months', label: 'Last 6 Months' },
      { value: 'last-year', label: 'Last Year' },
      { value: 'more-than-year', label: 'More than a Year' }
    ]
  };

  constructor(
    private qualityMeasuresService: QualityMeasuresService
  ) { }

  ngOnInit(): void {
    // Log the HIV and HCV status options for debugging
    console.log('HIV Status Options:', this.filterOptions.hivStatuses);
    console.log('HCV Status Options:', this.filterOptions.hcvStatuses);

    // Load filter configurations from mock API
    this.loadFilterConfigurations();

    // Initialize with default values
    this.applyFilters();
  }

  /**
   * Mock API method to simulate loading filter configurations from database
   * In the real implementation, this would call the qualityMeasuresService
   */
  private loadFilterConfigurations(): void {
    // Simulate API call delay
    setTimeout(() => {
      this.filterConfigurations = this.getMockFilterConfigurations();
      console.log('Filter configurations loaded:', this.filterConfigurations);
    }, 100);
  }

  /**
   * Mock filter configurations that simulate what the database-driven API would return
   * This maintains compatibility with existing filterOptions while providing metadata-driven structure
   */
  private getMockFilterConfigurations(): FilterConfiguration[] {
    return [
      {
        filterKey: 'measureGroup',
        displayName: 'Measure Group',
        controlType: 'dropdown',
        isEnabled: false,
        isReadOnly: false,
        isRequired: true,
        defaultValue: 'targeted',
        optionsSource: null, // Using embedded options
        embeddedOptions: this.filterOptions.measureGroups,
        sortOrder: 1,
        placeholderText: 'Select a measure group',
        helpText: 'Choose the quality measure group to filter reports'
      },
      {
        filterKey: 'year',
        displayName: 'Report Year',
        controlType: 'dropdown',
        isEnabled: true,
        isReadOnly: false,
        isRequired: true,
        defaultValue: '2024',
        optionsSource: null, // Could be '/api/lookups/years' in real implementation
        embeddedOptions: this.filterOptions.years,
        sortOrder: 2,
        placeholderText: 'Select a year'
      },
      {
        filterKey: 'quarter',
        displayName: 'Quarter',
        controlType: 'dropdown',
        isEnabled: true,
        isReadOnly: false,
        isRequired: false,
        defaultValue: 'any',
        optionsSource: null,
        embeddedOptions: this.filterOptions.quarters,
        sortOrder: 3,
        placeholderText: 'Select a quarter'
      },
      {
        filterKey: 'patientLocation',
        displayName: 'Patient Location',
        controlType: 'multiselect',
        isEnabled: true,
        isReadOnly: false,
        isRequired: false,
        defaultValue: 'any',
        optionsSource: '/api/lookups/locations', // This would use the existing location API
        embeddedOptions: this.filterOptions.patientLocations, // Fallback options
        sortOrder: 4,
        placeholderText: 'Select locations',
        helpText: 'Filter by patient location(s)'
      },
      {
        filterKey: 'provider',
        displayName: 'Provider',
        controlType: 'dropdown',
        isEnabled: true,
        isReadOnly: false,
        isRequired: false,
        defaultValue: 'any',
        optionsSource: null,
        embeddedOptions: this.filterOptions.providers,
        sortOrder: 5,
        placeholderText: 'Select a provider'
      },
      {
        filterKey: 'patientStatus',
        displayName: 'Patient Status',
        controlType: 'dropdown',
        isEnabled: true,
        isReadOnly: false,
        isRequired: false,
        defaultValue: 'active',
        optionsSource: null,
        embeddedOptions: this.filterOptions.patientStatuses,
        sortOrder: 6,
        placeholderText: 'Select patient status'
      },
      {
        filterKey: 'sex',
        displayName: 'Sex',
        controlType: 'dropdown',
        isEnabled: true,
        isReadOnly: false,
        isRequired: false,
        defaultValue: 'any',
        optionsSource: null,
        embeddedOptions: this.filterOptions.sexes,
        sortOrder: 7,
        placeholderText: 'Select sex'
      },
      {
        filterKey: 'hivStatus',
        displayName: 'HIV Status',
        controlType: 'dropdown',
        isEnabled: true,
        isReadOnly: false,
        isRequired: false,
        defaultValue: '-1',
        optionsSource: null,
        embeddedOptions: this.filterOptions.hivStatuses,
        sortOrder: 8,
        placeholderText: 'Select HIV status',
        helpText: 'Filter by HIV diagnosis status'
      },
      {
        filterKey: 'hcvStatus',
        displayName: 'HCV Status',
        controlType: 'dropdown',
        isEnabled: true,
        isReadOnly: false,
        isRequired: false,
        defaultValue: '-1',
        optionsSource: null,
        embeddedOptions: this.filterOptions.hcvStatuses,
        sortOrder: 9,
        placeholderText: 'Select HCV status',
        helpText: 'Filter by HCV diagnosis status'
      },
      {
        filterKey: 'insuranceType',
        displayName: 'Insurance Type',
        controlType: 'dropdown',
        isEnabled: true,
        isReadOnly: false,
        isRequired: false,
        defaultValue: 'any',
        optionsSource: null,
        embeddedOptions: this.filterOptions.insuranceTypes,
        sortOrder: 10,
        placeholderText: 'Select insurance type'
      },
      {
        filterKey: 'lastProviderVisit',
        displayName: 'Last Provider Visit',
        controlType: 'dropdown',
        isEnabled: true,
        isReadOnly: false,
        isRequired: false,
        defaultValue: 'any',
        optionsSource: null,
        embeddedOptions: this.filterOptions.lastProviderVisits,
        sortOrder: 11,
        placeholderText: 'Select visit timeframe'
      }
    ];
  }

  applyFilters(): void {
    // Emit the current filter values to the parent component
    this.filtersChanged.emit({...this.filters});
    console.log('Filters applied:', this.filters);
  }

  /**
   * Helper method to get enabled filter configurations sorted by sortOrder
   */
  getEnabledFilterConfigurations(): FilterConfiguration[] {
    return this.filterConfigurations
      .filter(config => config.isEnabled)
      .sort((a, b) => a.sortOrder - b.sortOrder);
  }

  /**
   * Helper method to get filter configuration by key
   */
  getFilterConfiguration(filterKey: string): FilterConfiguration | undefined {
    return this.filterConfigurations.find(config => config.filterKey === filterKey);
  }

  /**
   * Helper method to check if a filter is required
   */
  isFilterRequired(filterKey: string): boolean {
    const config = this.getFilterConfiguration(filterKey);
    return config ? config.isRequired : false;
  }

  /**
   * Helper method to get options for a specific filter
   * This would handle both embedded options and API-sourced options
   */
  getFilterOptions(filterKey: string): FilterOption[] {
    const config = this.getFilterConfiguration(filterKey);
    if (!config) return [];

    // If optionsSource is provided, this would make an API call
    // For now, return embedded options
    return config.embeddedOptions || [];
  }

  /**
   * Example method showing how to handle dynamic filter dependencies
   * This could be used when one filter's options depend on another filter's value
   */
  onFilterChange(filterKey: string, value: string): void {
    // Update the filter value
    (this.filters as any)[filterKey] = value;

    // Check for dependent filters and update their options if needed
    const dependentConfigs = this.filterConfigurations.filter(config =>
      config.dependsOn && config.dependsOn.includes(filterKey)
    );

    dependentConfigs.forEach(config => {
      // In a real implementation, this would trigger API calls to get updated options
      console.log(`Filter ${config.filterKey} depends on ${filterKey}, updating options...`);
    });

    // Apply the filters
    this.applyFilters();
  }
}
