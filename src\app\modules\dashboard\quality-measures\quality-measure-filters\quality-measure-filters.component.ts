import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { NgSelectModule } from '@ng-select/ng-select';
import { Observable, of, Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged, switchMap, catchError } from 'rxjs/operators';
import { QualityMeasuresService } from '../services/quality-measures.service';
import { HIVStatus, HCVStatus } from '../../panels/models/patient-criteria.model';

// Define interfaces for our filter options and values
export interface FilterOption {
  value: string;
  label: string;
}

// Interface for location options with optgroup support
export interface LocationOption {
  value: string;
  label: string;
  group?: string;
  disabled?: boolean;
}

// Interface for grouped location options
export interface LocationGroup {
  label: string;
  options: LocationOption[];
}

export interface QualityMeasureFilters {
  measureGroup: string;
  patientLocation: string;
  provider: string;
  year: string;
  quarter: string;
  patientStatus: string;
  sex: string;
  hivStatus: string;
  hcvStatus: string;
  insuranceType: string;
  lastProviderVisit: string;
}

// New interfaces for database-driven filter configuration
export interface FilterConfiguration {
  filterKey: string;
  displayName: string;
  controlType: 'dropdown' | 'multiselect' | 'text' | 'date' | 'number' | 'checkbox';
  isEnabled: boolean;
  isReadOnly: boolean;
  isRequired: boolean;
  defaultValue: string | null;
  optionsSource: string | null; // API endpoint or null for embedded options
  embeddedOptions?: FilterOption[]; // Embedded options when optionsSource is null
  sortOrder: number;
  placeholderText?: string;
  validationRules?: {
    minLength?: number;
    maxLength?: number;
    pattern?: string;
    min?: number;
    max?: number;
  };
  dependsOn?: string[]; // Array of filterKeys this filter depends on
  cssClass?: string;
  helpText?: string;
}

@Component({
  selector: 'app-quality-measure-filters',
  standalone: true,
  imports: [CommonModule, FormsModule, NgSelectModule],
  templateUrl: './quality-measure-filters.component.html',
  styleUrl: './quality-measure-filters.component.css'
})
export class QualityMeasureFiltersComponent implements OnInit {
  @Output() filtersChanged = new EventEmitter<QualityMeasureFilters>();

  // Initialize filters with default values
  filters: QualityMeasureFilters = {
    measureGroup: 'targeted',
    patientLocation: 'any',
    provider: 'any',
    year: '2025',
    quarter: 'any',
    patientStatus: 'active',
    sex: 'any',
    hivStatus: '-1', // Default value from HIVStatus (-1 = All)
    hcvStatus: '-1', // Default value from HCVStatus (-1 = All)
    insuranceType: 'any',
    lastProviderVisit: 'any'
  };

  // Database-driven filter configurations (mock API response)
  filterConfigurations: FilterConfiguration[] = [];

  // Location search functionality
  locationSearchInput$ = new Subject<string>();
  locationOptions$: Observable<LocationOption[]>;
  locationLoading = false;
  selectedLocations: string[] = [];

  // Filter options with default values
  filterOptions = {
    measureGroups: [
      { value: 'targeted', label: 'Targeted Quality Measures' },
      { value: 'HIV', label: 'HHS HIV Guidelines of Care' },
      { value: 'Other', label: 'Other Quality Measures' },
      { value: 'custom', label: 'Custom KPIs' },
      { value: 'PQRS', label: '2016 - PQRS Measures' },
      { value: 'MIPS', label: '2025 - MIPS Measures' },
      { value: 'VAX', label: 'Vaccines' },
      { value: 'Bonus', label: 'Bonus Measures' },
      { value: 'PCF', label: 'Primary Care First' }
    ],
    patientLocations: [
      { value: 'any', label: 'Any' }
      // Will be populated from API
    ],
    providers: [
      { value: 'any', label: 'Any' },
      { value: 'dr-smith', label: 'Dr. Smith' },
      { value: 'dr-johnson', label: 'Dr. Johnson' },
      { value: 'dr-williams', label: 'Dr. Williams' },
      { value: 'dr-brown', label: 'Dr. Brown' },
      { value: 'dr-jones', label: 'Dr. Jones' }
    ],
    years: [
      { value: 'any', label: 'Any' },
      { value: '2025', label: '2025' },
      { value: '2024', label: '2024' },
      { value: '2023', label: '2023' },
      { value: '2022', label: '2022' },
      { value: '2021', label: '2021' }
    ],
    quarters: [
      { value: 'any', label: 'Any' },
      { value: 'q1', label: 'Q1' },
      { value: 'q2', label: 'Q2' },
      { value: 'q3', label: 'Q3' },
      { value: 'q4', label: 'Q4' }
    ],
    patientStatuses: [
      { value: 'active', label: 'Active Patients' },
      { value: 'inactive', label: 'Inactive Patients' },
      { value: 'all', label: 'All Patients' }
    ],
    sexes: [
      { value: 'any', label: 'Any' },
      { value: 'male', label: 'Male' },
      { value: 'female', label: 'Female' },
      { value: 'other', label: 'Other' }
    ],
    // Using HIVStatus from patient-criteria.model.ts
    hivStatuses: HIVStatus.map(status => ({
      value: status.value,
      label: status.text
    })),
    // Using HCVStatus from patient-criteria.model.ts
    hcvStatuses: HCVStatus.map(status => ({
      value: status.value,
      label: status.text
    })),
    insuranceTypes: [
      { value: 'any', label: 'Any' },
      { value: 'commercial', label: 'Commercial Insurance' },
      { value: 'private', label: 'Private' },
      { value: 'medicaid', label: 'Medicaid' },
      { value: 'medicare', label: 'Medicare' },
      { value: 'medicare-advantage', label: 'Medicare Advantage' },
      { value: 'medicare-supplement', label: 'Medicare Supplement' },
      { value: 'not-classified', label: 'Not Classified' },
      { value: 'not-followed', label: 'Not Followed' },
      { value: 'other', label: 'Other' },
      { value: 'other-grant', label: 'Other Grant' },
      { value: 'pharma-study', label: 'Pharma Study' },
      { value: 'uninsured', label: 'Uninsured' },
      { value: 'va', label: 'Veterans Health Administration' }
    ],
    lastProviderVisits: [
      { value: 'any', label: 'Any' },
      { value: 'last-30-days', label: 'Last 30 Days' },
      { value: 'last-90-days', label: 'Last 90 Days' },
      { value: 'last-6-months', label: 'Last 6 Months' },
      { value: 'last-year', label: 'Last Year' },
      { value: 'more-than-year', label: 'More than a Year' }
    ]
  };

  constructor(
    private qualityMeasuresService: QualityMeasuresService
  ) {
    // Initialize location search observable
    this.locationOptions$ = this.locationSearchInput$.pipe(
      debounceTime(300),
      distinctUntilChanged(),
      switchMap(term => this.searchLocations(term)),
      catchError(() => of([]))
    );
  }

  ngOnInit(): void {
    // Log the HIV and HCV status options for debugging
    console.log('HIV Status Options:', this.filterOptions.hivStatuses);
    console.log('HCV Status Options:', this.filterOptions.hcvStatuses);

    // Load filter configurations from mock API
    this.loadFilterConfigurations();

    // Initialize location search with empty term to load initial options
    this.locationSearchInput$.next('');

    // Initialize with default values
    this.applyFilters();
  }

  /**
   * Mock API method to simulate loading filter configurations from database
   * In the real implementation, this would call the qualityMeasuresService
   */
  private loadFilterConfigurations(): void {
    // Simulate API call delay
    setTimeout(() => {
      this.filterConfigurations = this.getMockFilterConfigurations();
      console.log('Filter configurations loaded:', this.filterConfigurations);
    }, 100);
  }

  /**
   * Mock filter configurations that simulate what the database-driven API would return
   * This maintains compatibility with existing filterOptions while providing metadata-driven structure
   */
  private getMockFilterConfigurations(): FilterConfiguration[] {
    return [
      {
        filterKey: 'measureGroup',
        displayName: 'Measure Group',
        controlType: 'dropdown',
        isEnabled: true,
        isReadOnly: false,
        isRequired: true,
        defaultValue: 'targeted',
        optionsSource: null, // Using embedded options
        embeddedOptions: this.filterOptions.measureGroups,
        sortOrder: 1,
        placeholderText: 'Select a measure group',
        helpText: 'Choose the quality measure group to filter reports'
      },
      {
        filterKey: 'year',
        displayName: 'Report Year',
        controlType: 'dropdown',
        isEnabled: true,
        isReadOnly: false,
        isRequired: true,
        defaultValue: '2025',
        optionsSource: null, // Could be '/api/lookups/years' in real implementation
        embeddedOptions: this.filterOptions.years,
        sortOrder: 2,
        placeholderText: 'Select a year'
      },
      {
        filterKey: 'quarter',
        displayName: 'Quarter',
        controlType: 'dropdown',
        isEnabled: true,
        isReadOnly: false,
        isRequired: false,
        defaultValue: 'any',
        optionsSource: null,
        embeddedOptions: this.filterOptions.quarters,
        sortOrder: 3,
        placeholderText: 'Select a quarter'
      },
      {
        filterKey: 'patientLocation',
        displayName: 'Patient Location',
        controlType: 'multiselect',
        isEnabled: true,
        isReadOnly: false,
        isRequired: false,
        defaultValue: 'any',
        optionsSource: '/api/lookups/locations', // This would use the existing location API
        embeddedOptions: this.filterOptions.patientLocations, // Fallback options
        sortOrder: 4,
        placeholderText: 'Select locations',
        helpText: 'Filter by patient location(s)'
      },
      {
        filterKey: 'provider',
        displayName: 'Provider',
        controlType: 'dropdown',
        isEnabled: true,
        isReadOnly: false,
        isRequired: false,
        defaultValue: 'any',
        optionsSource: null,
        embeddedOptions: this.filterOptions.providers,
        sortOrder: 5,
        placeholderText: 'Select a provider'
      },
      {
        filterKey: 'patientStatus',
        displayName: 'Patient Status',
        controlType: 'dropdown',
        isEnabled: true,
        isReadOnly: false,
        isRequired: false,
        defaultValue: 'active',
        optionsSource: null,
        embeddedOptions: this.filterOptions.patientStatuses,
        sortOrder: 6,
        placeholderText: 'Select patient status'
      },
      {
        filterKey: 'sex',
        displayName: 'Sex',
        controlType: 'dropdown',
        isEnabled: true,
        isReadOnly: false,
        isRequired: false,
        defaultValue: 'any',
        optionsSource: null,
        embeddedOptions: this.filterOptions.sexes,
        sortOrder: 7,
        placeholderText: 'Select sex'
      },
      {
        filterKey: 'hivStatus',
        displayName: 'HIV Status',
        controlType: 'dropdown',
        isEnabled: true,
        isReadOnly: false,
        isRequired: false,
        defaultValue: '-1',
        optionsSource: null,
        embeddedOptions: this.filterOptions.hivStatuses,
        sortOrder: 8,
        placeholderText: 'Select HIV status',
        helpText: 'Filter by HIV diagnosis status'
      },
      {
        filterKey: 'hcvStatus',
        displayName: 'HCV Status',
        controlType: 'dropdown',
        isEnabled: true,
        isReadOnly: false,
        isRequired: false,
        defaultValue: '-1',
        optionsSource: null,
        embeddedOptions: this.filterOptions.hcvStatuses,
        sortOrder: 9,
        placeholderText: 'Select HCV status',
        helpText: 'Filter by HCV diagnosis status'
      },
      {
        filterKey: 'insuranceType',
        displayName: 'Insurance Type',
        controlType: 'dropdown',
        isEnabled: true,
        isReadOnly: false,
        isRequired: false,
        defaultValue: 'any',
        optionsSource: null,
        embeddedOptions: this.filterOptions.insuranceTypes,
        sortOrder: 10,
        placeholderText: 'Select insurance type'
      },
      {
        filterKey: 'lastProviderVisit',
        displayName: 'Last Provider Visit',
        controlType: 'dropdown',
        isEnabled: true,
        isReadOnly: false,
        isRequired: false,
        defaultValue: 'any',
        optionsSource: null,
        embeddedOptions: this.filterOptions.lastProviderVisits,
        sortOrder: 11,
        placeholderText: 'Select visit timeframe'
      }
    ];
  }

  applyFilters(): void {
    // Emit the current filter values to the parent component
    this.filtersChanged.emit({...this.filters});
    console.log('Filters applied:', this.filters);
  }

  /**
   * Helper method to get enabled filter configurations sorted by sortOrder
   */
  getEnabledFilterConfigurations(): FilterConfiguration[] {
    return this.filterConfigurations
      .filter(config => config.isEnabled)
      .sort((a, b) => a.sortOrder - b.sortOrder);
  }

  /**
   * Helper method to get filter configuration by key
   */
  getFilterConfiguration(filterKey: string): FilterConfiguration | undefined {
    return this.filterConfigurations.find(config => config.filterKey === filterKey);
  }

  /**
   * Helper method to check if a filter is required
   */
  isFilterRequired(filterKey: string): boolean {
    const config = this.getFilterConfiguration(filterKey);
    return config ? config.isRequired : false;
  }

  /**
   * Helper method to get options for a specific filter
   * This would handle both embedded options and API-sourced options
   */
  getFilterOptions(filterKey: string): FilterOption[] {
    const config = this.getFilterConfiguration(filterKey);
    if (!config) return [];

    // If optionsSource is provided, this would make an API call
    // For now, return embedded options
    return config.embeddedOptions || [];
  }

  /**
   * Handle filter changes with special logic for measureGroup changes
   * When measureGroup changes, reset all other filters to their default values
   */
  onFilterChange(filterKey: string, value: any): void {
    // Update the filter value
    (this.filters as any)[filterKey] = value;

    // Special handling for measureGroup changes - reset all other filters to defaults
    if (filterKey === 'measureGroup') {
      this.resetFiltersToDefaults(filterKey);
      console.log(`Measure Group changed to '${value}'. All other filters reset to defaults.`);
    }

    // Check for dependent filters and update their options if needed
    const dependentConfigs = this.filterConfigurations.filter(config =>
      config.dependsOn && config.dependsOn.includes(filterKey)
    );

    dependentConfigs.forEach(config => {
      // In a real implementation, this would trigger API calls to get updated options
      console.log(`Filter ${config.filterKey} depends on ${filterKey}, updating options...`);
    });

    // Apply the filters
    this.applyFilters();
  }

  /**
   * Reset all filters to their default values except for the specified excludeFilterKey
   */
  private resetFiltersToDefaults(excludeFilterKey?: string): void {
    this.filterConfigurations.forEach(config => {
      // Skip the filter that triggered the reset (e.g., measureGroup)
      if (excludeFilterKey && config.filterKey === excludeFilterKey) {
        return;
      }

      // Reset to default value from configuration
      if (config.defaultValue !== null) {
        (this.filters as any)[config.filterKey] = config.defaultValue;
      } else {
        // If no default value specified, use the original component defaults
        this.resetFilterToOriginalDefault(config.filterKey);
      }
    });
  }

  /**
   * Reset a specific filter to its original component default value
   */
  private resetFilterToOriginalDefault(filterKey: string): void {
    const originalDefaults: Partial<QualityMeasureFilters> = {
      measureGroup: 'targeted',
      patientLocation: 'any',
      provider: 'any',
      year: '2024',
      quarter: 'any',
      patientStatus: 'active',
      sex: 'any',
      hivStatus: '-1',
      hcvStatus: '-1',
      insuranceType: 'any',
      lastProviderVisit: 'any'
    };

    if (filterKey in originalDefaults) {
      (this.filters as any)[filterKey] = originalDefaults[filterKey as keyof QualityMeasureFilters];
    }
  }

  /**
   * Helper method to handle select change events
   */
  onSelectChange(filterKey: string, event: Event): void {
    const target = event.target as HTMLSelectElement;
    this.onFilterChange(filterKey, target.value);
  }

  /**
   * Helper method to handle input change events
   */
  onInputChange(filterKey: string, event: Event): void {
    const target = event.target as HTMLInputElement;
    this.onFilterChange(filterKey, target.value);
  }

  /**
   * Helper method to handle checkbox change events
   */
  onCheckboxChange(filterKey: string, event: Event): void {
    const target = event.target as HTMLInputElement;
    this.onFilterChange(filterKey, target.checked);
  }

  /**
   * Public method to reset all filters to their default values
   * This can be called from parent components or other external sources
   */
  resetAllFiltersToDefaults(): void {
    this.resetFiltersToDefaults();
    console.log('All filters reset to default values');
    this.applyFilters();
  }

  /**
   * Public method to reset all filters except measureGroup to their default values
   * Useful when you want to keep the current measureGroup but reset everything else
   */
  resetFiltersExceptMeasureGroup(): void {
    this.resetFiltersToDefaults('measureGroup');
    console.log('All filters except Measure Group reset to default values');
    this.applyFilters();
  }

  /**
   * Search locations using API call (mock implementation)
   * In real implementation, this would call the qualityMeasuresService
   */
  private searchLocations(searchTerm: string): Observable<LocationOption[]> {
    this.locationLoading = true;

    // Simulate API delay
    return new Observable(observer => {
      setTimeout(() => {
        const mockLocations = this.getMockLocationOptions(searchTerm);
        this.locationLoading = false;
        observer.next(mockLocations);
        observer.complete();
      }, 300);
    });
  }

  /**
   * Mock location data with optgroups
   * In real implementation, this would come from the API
   */
  private getMockLocationOptions(searchTerm: string): LocationOption[] {
    const allLocations: LocationOption[] = [
      // Main Campus locations
      { value: 'main-campus-er', label: 'Emergency Room', group: 'Main Campus' },
      { value: 'main-campus-icu', label: 'Intensive Care Unit', group: 'Main Campus' },
      { value: 'main-campus-cardiology', label: 'Cardiology Department', group: 'Main Campus' },
      { value: 'main-campus-oncology', label: 'Oncology Department', group: 'Main Campus' },
      { value: 'main-campus-surgery', label: 'Surgery Department', group: 'Main Campus' },

      // Outpatient Clinics
      { value: 'clinic-downtown', label: 'Downtown Clinic', group: 'Outpatient Clinics' },
      { value: 'clinic-westside', label: 'Westside Clinic', group: 'Outpatient Clinics' },
      { value: 'clinic-eastside', label: 'Eastside Clinic', group: 'Outpatient Clinics' },
      { value: 'clinic-north', label: 'North Clinic', group: 'Outpatient Clinics' },
      { value: 'clinic-south', label: 'South Clinic', group: 'Outpatient Clinics' },

      // Specialty Centers
      { value: 'heart-center', label: 'Heart Center', group: 'Specialty Centers' },
      { value: 'cancer-center', label: 'Cancer Center', group: 'Specialty Centers' },
      { value: 'womens-center', label: 'Women\'s Health Center', group: 'Specialty Centers' },
      { value: 'pediatric-center', label: 'Pediatric Center', group: 'Specialty Centers' },

      // Satellite Locations
      { value: 'satellite-metro', label: 'Metro Satellite', group: 'Satellite Locations' },
      { value: 'satellite-suburban', label: 'Suburban Satellite', group: 'Satellite Locations' },
      { value: 'satellite-rural', label: 'Rural Satellite', group: 'Satellite Locations' }
    ];

    // Filter based on search term
    if (!searchTerm || searchTerm.trim() === '') {
      return allLocations;
    }

    const filtered = allLocations.filter(location =>
      location.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
      location.group?.toLowerCase().includes(searchTerm.toLowerCase())
    );

    return filtered;
  }

  /**
   * Handle location selection changes
   */
  onLocationSelectionChange(selectedValues: string[]): void {
    this.selectedLocations = selectedValues || [];
    this.filters.patientLocation = selectedValues.length > 0 ? selectedValues.join(',') : 'any';
    this.applyFilters();
  }

  /**
   * Handle location search input
   */
  onLocationSearch(searchTerm: string): void {
    this.locationSearchInput$.next(searchTerm);
  }

  /**
   * Get grouped location options for ng-select
   */
  getGroupedLocationOptions(): Observable<LocationGroup[]> {
    return this.locationOptions$.pipe(
      switchMap(locations => {
        const grouped = this.groupLocationsByGroup(locations);
        return of(grouped);
      })
    );
  }

  /**
   * Group locations by their group property
   */
  private groupLocationsByGroup(locations: LocationOption[]): LocationGroup[] {
    const groups: { [key: string]: LocationOption[] } = {};

    locations.forEach(location => {
      const groupName = location.group || 'Other';
      if (!groups[groupName]) {
        groups[groupName] = [];
      }
      groups[groupName].push(location);
    });

    return Object.keys(groups).map(groupName => ({
      label: groupName,
      options: groups[groupName]
    }));
  }
}
