// Import the default ng-select theme first
@import '~@ng-select/ng-select/themes/default.theme.css';

// Custom ng-select theme for Quality Measures Filters
// This extends the default theme with our custom styling

// Variables for consistent theming
$primary-color: #0071bc;
$primary-light: #e3f2fd;
$primary-dark: #005a95;
$border-color: #ced4da;
$text-color: #333;
$placeholder-color: #6c757d;
$white: #fff;
$light-gray: #f8f9fa;
$border-gray: #e9ecef;

// Main ng-select container
.ng-select-custom {
  font-size: 0.9rem;
  font-family: MuseoSans-300, sans-serif;

  // Container styling
  .ng-select-container {
    min-height: 35px;
    border-radius: 5px;
    border: 1px solid $border-color;
    background-color: $white;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  }

  // Focused state
  &.ng-select-focused .ng-select-container {
    border-color: $primary-color;
    box-shadow: 0 0 0 0.25rem rgba(0, 113, 188, 0.25);
    outline: 0;
  }

  // Disabled state
  &.ng-select-disabled .ng-select-container {
    background-color: #e9ecef;
    opacity: 1;
  }

  // Placeholder styling
  .ng-placeholder {
    color: $placeholder-color;
    font-size: 0.9rem;
    display: block;
    line-height: 1.5;
  }

  // Value container
  .ng-value-container {
    padding-left: 12px;
    padding-right: 12px;
    align-items: center;

    .ng-input {
      > input {
        font-size: 0.9rem;
        color: $text-color;
        border: none;
        outline: none;
        background: transparent;
      }
    }
  }

  // Selected values (chips/tags)
  .ng-value {
    background-color: $primary-color;
    color: $white;
    border-radius: 3px;
    padding: 2px 8px;
    margin: 2px;
    font-size: 0.8rem;
    display: inline-flex;
    align-items: center;
    max-width: 100%;

    .ng-value-label {
      color: $white;
      font-size: 0.8rem;
    }

    .ng-value-icon {
      color: $white;
      margin-left: 6px;
      cursor: pointer;
      border-radius: 50%;
      width: 16px;
      height: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;

      &:hover {
        background-color: rgba(255, 255, 255, 0.2);
      }

      &.left {
        margin-left: 0;
        margin-right: 6px;
      }
    }
  }

  // Arrow styling
  .ng-arrow-wrapper {
    width: 25px;
    padding-right: 5px;

    .ng-arrow {
      border-color: #999 transparent transparent;
      border-style: solid;
      border-width: 5px 5px 0;
      transition: transform 0.2s ease;
    }
  }

  // Opened state arrow
  &.ng-select-opened .ng-arrow-wrapper .ng-arrow {
    border-color: transparent transparent #999;
    border-width: 0 5px 5px;
  }

  // Clear button
  .ng-clear-wrapper {
    color: $placeholder-color;
    cursor: pointer;
    width: 17px;
    height: 17px;
    margin-right: 5px;

    &:hover {
      color: $primary-color;
    }
  }

  // Dropdown panel
  .ng-dropdown-panel {
    border: 1px solid $border-color;
    border-radius: 5px;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    background: $white;
    z-index: 1050;

    // Dropdown items container
    .ng-dropdown-panel-items {
      max-height: 300px;
      overflow-y: auto;

      // Option groups
      .ng-optgroup {
        font-weight: 600;
        color: $primary-color;
        padding: 8px 12px 4px;
        font-size: 0.85rem;
        background-color: $light-gray;
        border-bottom: 1px solid $border-gray;
        user-select: none;
        cursor: default;
      }

      // Individual options
      .ng-option {
        padding: 8px 12px;
        font-size: 0.9rem;
        color: $text-color;
        cursor: pointer;
        user-select: none;
        transition: background-color 0.15s ease;

        // Highlighted option (hover)
        &.ng-option-highlighted {
          background-color: $primary-light;
          color: $primary-color;
        }

        // Selected option
        &.ng-option-selected {
          background-color: $primary-color;
          color: $white;
        }

        // Disabled option
        &.ng-option-disabled {
          color: $placeholder-color;
          cursor: not-allowed;
          background-color: transparent;
        }

        // Child options (indented)
        &.ng-option-child {
          padding-left: 24px;
        }
      }
    }
  }

  // Loading spinner
  .ng-spinner-loader {
    border: 2px solid transparent;
    border-top: 2px solid $primary-color;
    border-radius: 50%;
    width: 16px;
    height: 16px;
    animation: spin 1s linear infinite;
    margin-right: 5px;
  }

  // Search input in dropdown
  .ng-dropdown-header {
    padding: 8px 12px;
    border-bottom: 1px solid $border-gray;

    input {
      width: 100%;
      border: 1px solid $border-color;
      border-radius: 3px;
      padding: 4px 8px;
      font-size: 0.9rem;
      outline: none;

      &:focus {
        border-color: $primary-color;
        box-shadow: 0 0 0 0.125rem rgba(0, 113, 188, 0.25);
      }
    }
  }

  // Footer (for actions like "Select All")
  .ng-dropdown-footer {
    padding: 8px 12px;
    border-top: 1px solid $border-gray;
    background-color: $light-gray;
  }

  // No options found message
  .ng-option[data-selectable="false"] {
    color: $placeholder-color;
    font-style: italic;
    cursor: default;
    padding: 12px;
    text-align: center;
  }
}

// Spinner animation
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Multiple selection specific styles
.ng-select-custom.ng-select-multiple {
  .ng-select-container {
    .ng-value-container {
      .ng-value {
        margin: 1px 2px;
      }
    }
  }
}

// Single selection specific styles
.ng-select-custom.ng-select-single {
  .ng-select-container {
    height: 35px;
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .ng-select-custom {
    .ng-dropdown-panel {
      .ng-dropdown-panel-items {
        max-height: 250px;
      }
    }
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .ng-select-custom {
    .ng-select-container {
      border-width: 2px;
    }

    &.ng-select-focused .ng-select-container {
      border-width: 2px;
    }
  }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  .ng-select-custom {
    .ng-select-container,
    .ng-arrow,
    .ng-value {
      transition: none;
    }
  }
}
